import { useState, useEffect, useCallback } from 'react';

// 资源预加载Hook
export const useResourcePreloader = () => {
  const [loadingState, setLoadingState] = useState({
    isLoading: true,
    progress: 0,
    stage: 'Initializing',
    error: null
  });

  // 需要预加载的资源列表
  const resources = [
    // 地理数据文件
    { url: '/welcomeEarthLandData/ne_50m_land.json', type: 'json', weight: 30 },
    { url: '/welcomeEarthLandData/ne_50m_admin_0_countries.json', type: 'json', weight: 30 },
    
    // 纹理文件
    { url: '/textures/earth/earth_blue_techmap.jpg', type: 'image', weight: 15 },
    { url: '/textures/earth/earth_normal.jpg', type: 'image', weight: 10 },
    { url: '/textures/earth/earth_ao.jpg', type: 'image', weight: 5 },
    { url: '/textures/earth/earth_displacement.jpg', type: 'image', weight: 5 },
    { url: '/textures/earth/earth_roughness.jpg', type: 'image', weight: 5 }
  ];

  // 加载单个资源
  const loadResource = useCallback((resource) => {
    return new Promise((resolve, reject) => {
      const { url, type } = resource;
      
      if (type === 'json') {
        fetch(url)
          .then(response => {
            if (!response.ok) throw new Error(`Failed to load ${url}`);
            return response.json();
          })
          .then(data => resolve({ url, data, type }))
          .catch(reject);
      } else if (type === 'image') {
        const img = new Image();
        img.onload = () => resolve({ url, data: img, type });
        img.onerror = () => reject(new Error(`Failed to load image ${url}`));
        img.src = url;
      }
    });
  }, []);

  // 预加载所有资源
  const preloadResources = useCallback(async () => {
    try {
      setLoadingState(prev => ({ ...prev, stage: 'Loading geographic data' }));
      
      const totalWeight = resources.reduce((sum, resource) => sum + resource.weight, 0);
      let loadedWeight = 0;
      const loadedResources = {};

      // 并行加载资源，但控制并发数量
      const concurrencyLimit = 3;
      const chunks = [];
      for (let i = 0; i < resources.length; i += concurrencyLimit) {
        chunks.push(resources.slice(i, i + concurrencyLimit));
      }

      for (const chunk of chunks) {
        const promises = chunk.map(async (resource) => {
          try {
            const result = await loadResource(resource);
            loadedResources[resource.url] = result.data;
            loadedWeight += resource.weight;
            
            const progress = Math.min((loadedWeight / totalWeight) * 100, 100);
            
            // 更新加载阶段
            let stage = 'Loading geographic data';
            if (progress > 60) stage = 'Initializing 3D engine';
            if (progress > 80) stage = 'Preparing visualization';
            if (progress > 95) stage = 'Finalizing';
            
            setLoadingState(prev => ({
              ...prev,
              progress,
              stage
            }));
            
            return result;
          } catch (error) {
            console.warn(`Failed to load resource ${resource.url}:`, error);
            // 继续加载其他资源，不因单个资源失败而停止
            loadedWeight += resource.weight * 0.5; // 给失败的资源一半权重
            return null;
          }
        });

        await Promise.allSettled(promises);
      }

      // 模拟最后的初始化时间
      setLoadingState(prev => ({ ...prev, stage: 'Ready', progress: 100 }));
      
      // 短暂延迟确保用户看到100%
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setLoadingState(prev => ({ ...prev, isLoading: false }));
      
      return loadedResources;
      
    } catch (error) {
      console.error('Resource preloading failed:', error);
      setLoadingState(prev => ({
        ...prev,
        error: error.message,
        isLoading: false
      }));
      throw error;
    }
  }, [loadResource, resources]);

  // 开始预加载
  useEffect(() => {
    preloadResources();
  }, [preloadResources]);

  return loadingState;
};

// 资源缓存管理
export const resourceCache = new Map();

// 获取缓存的资源
export const getCachedResource = (url) => {
  return resourceCache.get(url);
};

// 设置资源缓存
export const setCachedResource = (url, data) => {
  resourceCache.set(url, data);
};

// 预加载关键CSS和字体
export const preloadCriticalResources = () => {
  // 预加载Google Fonts
  const fontLinks = [
    'https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700&display=swap',
    'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap'
  ];

  fontLinks.forEach(href => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'style';
    link.href = href;
    link.onload = () => {
      link.rel = 'stylesheet';
    };
    document.head.appendChild(link);
  });

  // 预加载关键图片
  const criticalImages = [
    '/textures/earth/earth_blue_techmap.jpg'
  ];

  criticalImages.forEach(src => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    document.head.appendChild(link);
  });
};

export default useResourcePreloader;
